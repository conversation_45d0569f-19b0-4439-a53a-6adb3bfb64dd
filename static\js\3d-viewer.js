/**
 * 3D Model Viewer using Three.js
 * Handles loading and displaying OBJ files in the web interface
 * Fixed to rotate around model center like CAD software
 */

class ModelViewer3D {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.currentModel = null;
        this.isWireframe = false;
        this.modelCenter = new THREE.Vector3(0, 0, 0);
        this.modelBoundingBox = null;
        this.selectedMesh = null;
        this.originalColor = null;
        this.grid = null;
        this.modelInfo = {
            vertices: 0,
            faces: 0,
            fileName: ''
        };
        this.controlsInfo = {
            left: "Left click: Rotate/Orbit",
            wheel: "Wheel: Zoom in/out",
            middle: "Middle click or Shift+Left: Pan view"
        };

        this.init();
        this.setupEventListeners();
    }

    init() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0xf0f0f0);

        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            45,
            this.container.clientWidth / this.container.clientHeight,
            0.1,
            1000
        );
        this.camera.position.set(5, 5, 5);

        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // Add renderer to container
        const viewerElement = this.container.querySelector('.viewer-3d');
        viewerElement.appendChild(this.renderer.domElement);

        // Create controls
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.1;
        this.controls.rotateSpeed = 0.7;
        this.controls.panSpeed = 0.7;
        this.controls.zoomSpeed = 1.2;
        this.controls.screenSpacePanning = true;
        this.controls.enableZoom = true;
        this.controls.enablePan = true;

        // CAD-like controls configuration
        this.controls.mouseButtons = {
            LEFT: THREE.MOUSE.ROTATE,       // Left click: rotate/orbit
            MIDDLE: THREE.MOUSE.PAN,        // Middle click: pan
            RIGHT: THREE.MOUSE.PAN          // Right click: pan (alternative)
        };

        // Enable Shift + Left click for panning (common CAD behavior)
        this.controls.keyPanSpeed = 10.0;
        this.controls.keys = {
            LEFT: "ArrowLeft",
            UP: "ArrowUp",
            RIGHT: "ArrowRight",
            BOTTOM: "ArrowDown"
        };

        // Setup grid
        this.setupGrid();

        // Add lights
        this.setupLighting();

        // Start render loop
        this.animate();

        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
    }

    setupLighting() {
        // Lighting giống OpenCASCADE

        // Ambient light - ánh sáng xung quanh mạnh hơn
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.65);
        this.scene.add(ambientLight);

        // Directional lights từ 3 hướng chính như OpenCASCADE
        const directionalLight1 = new THREE.DirectionalLight(0xffffff, 0.5);
        directionalLight1.position.set(10, 10, 10);
        directionalLight1.castShadow = true;
        directionalLight1.shadow.mapSize.width = 2048;
        directionalLight1.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight1);

        const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.35);
        directionalLight2.position.set(-10, 5, -5);
        this.scene.add(directionalLight2);

        const directionalLight3 = new THREE.DirectionalLight(0xffffff, 0.25);
        directionalLight3.position.set(5, -10, -7);
        this.scene.add(directionalLight3);
    }

    setupEventListeners() {
        // Reset camera button
        document.getElementById('reset-camera-btn')?.addEventListener('click', () => {
            this.resetCamera();
        });

        // Standard view buttons
        document.getElementById('view-top-btn')?.addEventListener('click', () => {
            this.setStandardView('top');
        });

        document.getElementById('view-bottom-btn')?.addEventListener('click', () => {
            this.setStandardView('bottom');
        });

        document.getElementById('view-front-btn')?.addEventListener('click', () => {
            this.setStandardView('front');
        });

        document.getElementById('view-back-btn')?.addEventListener('click', () => {
            this.setStandardView('back');
        });

        document.getElementById('view-left-btn')?.addEventListener('click', () => {
            this.setStandardView('left');
        });

        document.getElementById('view-right-btn')?.addEventListener('click', () => {
            this.setStandardView('right');
        });

        // Wireframe toggle button
        document.getElementById('wireframe-toggle-btn')?.addEventListener('click', () => {
            this.toggleWireframe();
        });

        // Download OBJ button
        document.getElementById('download-obj-btn')?.addEventListener('click', () => {
            this.downloadCurrentModel();
        });

        // Fullscreen button
        document.getElementById('fullscreen-viewer-btn')?.addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // Code toggle button
        document.getElementById('toggle-code-btn')?.addEventListener('click', () => {
            this.toggleCodeOutput();
        });

        // Thêm xử lý click để hiển thị thông tin model
        const viewerElement = this.container.querySelector('.viewer-3d');
        if (viewerElement) {
            viewerElement.addEventListener('pointerdown', this.onModelClick.bind(this));
        }
    }

    async loadOBJFile(objUrl, fileName = '') {
        try {
            this.showLoading(true);
            this.hideControls();

            // Remove previous model
            if (this.currentModel) {
                this.scene.remove(this.currentModel);
            }

            // Load OBJ file
            const loader = new THREE.OBJLoader();

            return new Promise((resolve, reject) => {
                loader.load(
                    objUrl,
                    (object) => {
                        this.currentModel = object;

                        // Apply default material
                        object.traverse((child) => {
                            if (child.isMesh) {
                                child.material = new THREE.MeshLambertMaterial({
                                    color: 0x888888,
                                    side: THREE.DoubleSide
                                });
                                child.castShadow = true;
                                child.receiveShadow = true;
                            }
                        });

                        // Add to scene
                        this.scene.add(object);

                        // Center and scale the model - FIXED VERSION
                        this.centerAndScaleModel(object);

                        // Update model info
                        this.updateModelInfo(object, fileName);

                        // Show controls and info
                        this.showControls();
                        this.showInfo();
                        this.hideLoading();

                        // Update control info with correct orbit center
                        this.updateControlsInfoDisplay();

                        console.log('3D model loaded successfully:', fileName);
                        resolve(object);
                    },
                    (progress) => {
                        console.log('Loading progress:', (progress.loaded / progress.total * 100) + '%');
                    },
                    (error) => {
                        console.error('Error loading OBJ file:', error);
                        this.hideLoading();
                        this.showError('Failed to load 3D model');
                        reject(error);
                    }
                );
            });
        } catch (error) {
            console.error('Error in loadOBJFile:', error);
            this.hideLoading();
            this.showError('Failed to load 3D model');
            throw error;
        }
    }

    centerAndScaleModel(object) {
        // Calculate bounding box BEFORE any transformations
        const box = new THREE.Box3().setFromObject(object);
        const center = box.getCenter(new THREE.Vector3());
        const size = box.getSize(new THREE.Vector3());

        // Store the ORIGINAL center point for orbit controls
        this.modelCenter = center.clone();
        this.modelBoundingBox = box.clone();

        // Move model so its center is at origin
        object.position.sub(center);

        // Adjust Y position so model sits on the grid
        const minY = box.min.y - center.y; // Relative to new position
        if (minY < 0) {
            object.position.y -= minY - 0.01; // Lift slightly above grid
            // Update model center to reflect this adjustment
            this.modelCenter.y += (-minY + 0.01);
        }

        // Scale the model to fit nicely in view
        const maxDim = Math.max(size.x, size.y, size.z);
        const scale = 5 / maxDim;
        object.scale.setScalar(scale);

        // Scale the model center accordingly
        this.modelCenter.multiplyScalar(scale);

        // After positioning and scaling, the visual center is at (0, y_offset, 0)
        // Set orbit controls to rotate around the visual center of the scaled model
        const visualCenter = new THREE.Vector3(0, object.position.y + (size.y * scale) / 2, 0);
        this.controls.target.copy(visualCenter);

        // Update grid to match model size
        this.updateGridToMatchModel(maxDim * scale);

        // Position camera for good initial view
        this.resetCamera();

        // Force controls update
        this.controls.update();
    }

    // Cập nhật grid theo kích thước mô hình
    updateGridToMatchModel(modelSize) {
        // Xóa grid cũ
        if (this.grid) {
            this.scene.remove(this.grid);
        }

        // Tính toán kích thước grid phù hợp
        const gridSize = Math.max(20, modelSize * 2);
        const gridDivisions = 20;
        const gridColor = 0x888888;

        // Tạo grid mới
        this.grid = new THREE.GridHelper(gridSize, gridDivisions, gridColor, gridColor);
        this.grid.material.opacity = 0.25;
        this.grid.material.transparent = true;
        this.grid.position.y = -0.01; // Đặt grid ngay dưới mô hình một chút
        this.scene.add(this.grid);
    }

    updateModelInfo(object, fileName) {
        let vertices = 0;
        let triangleFaces = 0;
        let actualFaces = 0;

        object.traverse((child) => {
            if (child.isMesh && child.geometry) {
                if (child.geometry.attributes.position) {
                    vertices += child.geometry.attributes.position.count;
                }

                // Count triangular faces
                let childTriangleFaces = 0;
                if (child.geometry.index) {
                    childTriangleFaces = child.geometry.index.count / 3;
                } else if (child.geometry.attributes.position) {
                    childTriangleFaces = child.geometry.attributes.position.count / 3;
                }
                triangleFaces += childTriangleFaces;

                // Calculate actual faces by grouping coplanar triangles
                actualFaces += this.calculateActualFaces(child.geometry);
            }
        });

        this.modelInfo = {
            vertices: Math.floor(vertices),
            faces: Math.floor(triangleFaces), // Keep triangle count for reference
            actualFaces: Math.floor(actualFaces), // Real face count
            fileName: fileName || 'model.obj'
        };

        this.updateInfoDisplay();
    }

    calculateActualFaces(geometry) {
        if (!geometry.attributes.position) return 0;

        const positions = geometry.attributes.position.array;
        const indices = geometry.index ? geometry.index.array : null;

        // Get all triangles
        const triangles = [];
        const triangleCount = indices ? indices.length / 3 : positions.length / 9;

        for (let i = 0; i < triangleCount; i++) {
            let v1, v2, v3;

            if (indices) {
                const i1 = indices[i * 3] * 3;
                const i2 = indices[i * 3 + 1] * 3;
                const i3 = indices[i * 3 + 2] * 3;

                v1 = new THREE.Vector3(positions[i1], positions[i1 + 1], positions[i1 + 2]);
                v2 = new THREE.Vector3(positions[i2], positions[i2 + 1], positions[i2 + 2]);
                v3 = new THREE.Vector3(positions[i3], positions[i3 + 1], positions[i3 + 2]);
            } else {
                const base = i * 9;
                v1 = new THREE.Vector3(positions[base], positions[base + 1], positions[base + 2]);
                v2 = new THREE.Vector3(positions[base + 3], positions[base + 4], positions[base + 5]);
                v3 = new THREE.Vector3(positions[base + 6], positions[base + 7], positions[base + 8]);
            }

            // Calculate triangle normal
            const edge1 = new THREE.Vector3().subVectors(v2, v1);
            const edge2 = new THREE.Vector3().subVectors(v3, v1);
            const normal = new THREE.Vector3().crossVectors(edge1, edge2).normalize();

            // Calculate triangle center
            const center = new THREE.Vector3()
                .addVectors(v1, v2)
                .add(v3)
                .divideScalar(3);

            triangles.push({
                vertices: [v1, v2, v3],
                normal: normal,
                center: center,
                used: false
            });
        }

        // Group coplanar triangles into faces
        let faceCount = 0;
        const tolerance = 0.01; // Tolerance for normal comparison
        const distanceTolerance = 0.01; // Tolerance for plane distance

        for (let i = 0; i < triangles.length; i++) {
            if (triangles[i].used) continue;

            const currentTriangle = triangles[i];
            triangles[i].used = true;
            faceCount++;

            // Find all triangles that are coplanar with this one
            for (let j = i + 1; j < triangles.length; j++) {
                if (triangles[j].used) continue;

                const otherTriangle = triangles[j];

                // Check if normals are similar (coplanar)
                const normalDot = Math.abs(currentTriangle.normal.dot(otherTriangle.normal));
                if (normalDot > (1 - tolerance)) {
                    // Check if triangles are on the same plane
                    const centerDiff = new THREE.Vector3().subVectors(otherTriangle.center, currentTriangle.center);
                    const distanceToPlane = Math.abs(centerDiff.dot(currentTriangle.normal));

                    if (distanceToPlane < distanceTolerance) {
                        triangles[j].used = true;
                    }
                }
            }
        }

        return faceCount;
    }

    updateInfoDisplay() {
        const infoElement = document.getElementById('model-info-text');
        if (infoElement) {
            this.originalInfoContent = `
                <div class="occ-style-info">
                    <strong>${this.modelInfo.fileName}</strong>
                    <table class="mt-1 text-xs w-full">
                        <tr>
                            <td class="pr-2">Vertices:</td>
                            <td>${this.modelInfo.vertices.toLocaleString()}</td>
                        </tr>
                        <tr>
                            <td class="pr-2">Faces:</td>
                            <td>${this.modelInfo.faces.toLocaleString()}</td>
                        </tr>
                    </table>
                    <hr class="my-1 border-gray-300">
                    <div class="text-xs">
                        <b>Điều khiển chuột:</b>
                        <div class="ml-1 mt-1">
                            <div>• Chuột trái: Xoay mô hình</div>
                            <div>• Chuột phải: Di chuyển khung nhìn</div>
                            <div>• Chuột giữa: Di chuyển khung nhìn</div>
                            <div>• Lăn chuột: Phóng to/thu nhỏ</div>
                            <div>• Click chuột trái: Chọn mặt</div>
                        </div>
                    </div>
                </div>
            `;

            infoElement.innerHTML = this.originalInfoContent;
        }
    }

    resetCamera() {
        if (this.currentModel) {
            // Get the current bounding box of the positioned and scaled model
            const box = new THREE.Box3().setFromObject(this.currentModel);
            const center = box.getCenter(new THREE.Vector3());
            const size = box.getSize(new THREE.Vector3());
            const maxDim = Math.max(size.x, size.y, size.z);

            // Position camera in isometric view like CAD software
            const distance = maxDim * 2.5;
            this.camera.position.set(
                center.x + distance * 0.7,
                center.y + distance * 0.7,
                center.z + distance * 0.7
            );

            // Look at and orbit around the visual center of the model
            this.camera.lookAt(center);
            this.controls.target.copy(center);

        } else {
            // Default camera position if no model
            this.camera.position.set(15, 10, 15);
            this.camera.lookAt(0, 0, 0);
            this.controls.target.set(0, 0, 0);
        }

        this.controls.update();
    }

    toggleWireframe() {
        if (!this.currentModel) return;

        this.isWireframe = !this.isWireframe;

        this.currentModel.traverse((child) => {
            if (child.isMesh) {
                child.material.wireframe = this.isWireframe;
            }
        });

        // Update button appearance
        const btn = document.getElementById('wireframe-toggle-btn');
        if (btn) {
            btn.style.background = this.isWireframe ? 'rgba(59, 130, 246, 0.9)' : 'rgba(255, 255, 255, 0.9)';
            btn.style.color = this.isWireframe ? 'white' : '#374151';
        }
    }

    downloadCurrentModel() {
        // This would trigger download of the current OBJ file
        if (this.modelInfo.fileName) {
            const downloadUrl = `/download/outputs/obj/${this.modelInfo.fileName}`;
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = this.modelInfo.fileName;
            link.click();
        }
    }

    toggleFullscreen() {
        // Implementation for fullscreen mode
        const container = this.container;
        if (!document.fullscreenElement) {
            container.requestFullscreen().then(() => {
                this.onWindowResize();
            });
        } else {
            document.exitFullscreen();
        }
    }

    toggleCodeOutput() {
        const container = document.getElementById('code-output-container');
        const icon = document.querySelector('#toggle-code-btn i.fa-chevron-down');

        if (container.classList.contains('hidden')) {
            container.classList.remove('hidden');
            icon.style.transform = 'rotate(180deg)';
        } else {
            container.classList.add('hidden');
            icon.style.transform = 'rotate(0deg)';
        }
    }

    showLoading(show = true) {
        const loading = document.getElementById('viewer-loading');
        const placeholder = this.container.querySelector('.viewer-placeholder');

        if (show) {
            loading?.classList.remove('hidden');
            placeholder?.classList.add('hidden');
        } else {
            loading?.classList.add('hidden');
        }
    }

    hideLoading() {
        this.showLoading(false);
    }

    showControls() {
        document.getElementById('viewer-controls')?.classList.remove('hidden');
        document.getElementById('fullscreen-viewer-btn')?.classList.remove('hidden');
    }

    hideControls() {
        document.getElementById('viewer-controls')?.classList.add('hidden');
        document.getElementById('fullscreen-viewer-btn')?.classList.add('hidden');
    }

    showInfo() {
        document.getElementById('viewer-info')?.classList.remove('hidden');
    }

    showError(message) {
        const placeholder = this.container.querySelector('.viewer-placeholder');
        if (placeholder) {
            placeholder.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                <p class="text-sm">${message}</p>
                <p class="text-xs opacity-75 mt-1">Please try generating the model again</p>
            `;
            placeholder.classList.remove('hidden');
        }
    }

    onWindowResize() {
        if (!this.camera || !this.renderer) return;

        this.camera.aspect = this.container.clientWidth / this.container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        if (this.controls) {
            this.controls.update();
        }

        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }

    // Public method to load a model from URL
    async loadModel(objUrl, fileName) {
        try {
            await this.loadOBJFile(objUrl, fileName);
            return true;
        } catch (error) {
            console.error('Failed to load model:', error);
            return false;
        }
    }

    // Clear the current model
    clearModel() {
        if (this.currentModel) {
            this.scene.remove(this.currentModel);
            this.currentModel = null;

            // Reset model center
            this.modelCenter.set(0, 0, 0);
            this.modelBoundingBox = null;
        }

        this.hideControls();

        // Update info to show just controls
        const infoElement = document.getElementById('model-info-text');
        if (infoElement) {
            infoElement.innerHTML = `
                <strong>No model loaded</strong><br>
                <hr class="my-1 border-gray-300">
                <div class="text-xs mt-1">
                    ${this.controlsInfo.left}<br>
                    ${this.controlsInfo.wheel}<br>
                    ${this.controlsInfo.middle}
                </div>
            `;
        }

        // Keep the info panel visible to show controls help
        document.getElementById('viewer-info')?.classList.remove('hidden');

        const placeholder = this.container.querySelector('.viewer-placeholder');
        if (placeholder) {
            placeholder.innerHTML = `
                <i class="fas fa-cube"></i>
                <p class="text-sm">3D model will appear here</p>
                <p class="text-xs opacity-75 mt-1">Generate a model to see the preview</p>
            `;
            placeholder.classList.remove('hidden');
        }
    }

    // Set standard camera views
    setStandardView(viewType) {
        if (!this.currentModel) return;

        // Get current model center and size
        const box = new THREE.Box3().setFromObject(this.currentModel);
        const center = box.getCenter(new THREE.Vector3());
        const size = box.getSize(new THREE.Vector3());
        const maxDim = Math.max(size.x, size.y, size.z) * 2;

        switch (viewType) {
            case 'top':
                this.camera.position.set(center.x, center.y + maxDim, center.z);
                break;
            case 'bottom':
                this.camera.position.set(center.x, center.y - maxDim, center.z);
                break;
            case 'front':
                this.camera.position.set(center.x, center.y, center.z + maxDim);
                break;
            case 'back':
                this.camera.position.set(center.x, center.y, center.z - maxDim);
                break;
            case 'left':
                this.camera.position.set(center.x - maxDim, center.y, center.z);
                break;
            case 'right':
                this.camera.position.set(center.x + maxDim, center.y, center.z);
                break;
            default:
                return;
        }

        // Look at model center
        this.camera.lookAt(center);
        this.controls.target.copy(center);
        this.controls.update();
    }

    // Helper method to update controls info display
    updateControlsInfoDisplay() {
        if (!this.currentModel) return;

        const box = new THREE.Box3().setFromObject(this.currentModel);
        const center = box.getCenter(new THREE.Vector3());

        const infoText = `
            <div class="text-xs mt-1">
                <b>Điều khiển:</b><br>
                ${this.controlsInfo.left}<br>
                ${this.controlsInfo.wheel}<br>
                ${this.controlsInfo.middle}<br>
                <span style="color: #ffcc00;">Xoay quanh: ${center.x.toFixed(2)}, ${center.y.toFixed(2)}, ${center.z.toFixed(2)}</span>
            </div>
        `;

        // Append to model info if exists
        const infoElement = document.getElementById('model-info-text');
        if (infoElement && infoElement.innerHTML) {
            const currentInfo = infoElement.innerHTML;
            if (!currentInfo.includes('Điều khiển:')) {
                infoElement.innerHTML += `<hr class="my-1 border-gray-300">` + infoText;
            }
        }
    }

    // Xử lý click vào model
    onModelClick(event) {
        if (!this.currentModel || event.button !== 0) return; // Chỉ xử lý chuột trái

        // Use renderer.domElement for consistent coordinate calculation relative to the canvas
        const rect = this.renderer.domElement.getBoundingClientRect();
        const x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        const y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        const raycaster = new THREE.Raycaster();
        const mouse = new THREE.Vector2(x, y);
        raycaster.setFromCamera(mouse, this.camera);

        const intersects = raycaster.intersectObject(this.currentModel, true);

        // Clear any ongoing flash from a previous selection/state
        if (this.flashIntervalId) {
            clearInterval(this.flashIntervalId);
            this.flashIntervalId = null;
        }

        // Remove previous highlight face if exists
        if (this.highlightFaceMesh) {
            this.scene.remove(this.highlightFaceMesh);
            this.highlightFaceMesh = null;
        }

        if (intersects.length > 0) {
            const intersection = intersects[0];
            const face = intersection.face;
            const point = intersection.point;
            const object = intersection.object; // The intersected THREE.Mesh

            if (!face || !object.geometry || !object.geometry.attributes.position) {
                console.warn("Intersected object or face is missing required data for selection.");
                this.updateInfoDisplay();
                return;
            }

            // Get vertices of the clicked face
            const positionAttribute = object.geometry.attributes.position;
            const vA = new THREE.Vector3().fromBufferAttribute(positionAttribute, face.a);
            const vB = new THREE.Vector3().fromBufferAttribute(positionAttribute, face.b);
            const vC = new THREE.Vector3().fromBufferAttribute(positionAttribute, face.c);

            // Create a new geometry just for this face
            const faceGeometry = new THREE.BufferGeometry();
            const vertices = new Float32Array([
                vA.x, vA.y, vA.z,
                vB.x, vB.y, vB.z,
                vC.x, vC.y, vC.z
            ]);

            faceGeometry.setAttribute('position', new THREE.BufferAttribute(vertices, 3));
            faceGeometry.computeVertexNormals();

            // Create a highlight material
            const highlightMaterial = new THREE.MeshBasicMaterial({
                color: 0xffdd00,
                side: THREE.DoubleSide,
                transparent: true,
                opacity: 0.8,
                depthTest: false   // To ensure it's visible even if slightly behind other geometry
            });

            // Create a mesh for the highlighted face
            this.highlightFaceMesh = new THREE.Mesh(faceGeometry, highlightMaterial);

            // Apply the object's transformation matrix to position the highlight correctly
            this.highlightFaceMesh.applyMatrix4(object.matrixWorld);

            // Add a small offset to prevent z-fighting
            const normal = new THREE.Vector3(face.normal.x, face.normal.y, face.normal.z);
            normal.transformDirection(object.matrixWorld);
            normal.multiplyScalar(0.005); // Small offset along the normal

            this.highlightFaceMesh.position.add(normal);

            // Add to scene
            this.scene.add(this.highlightFaceMesh);

            // Transform vertices to world space for display
            vA.applyMatrix4(object.matrixWorld);
            vB.applyMatrix4(object.matrixWorld);
            vC.applyMatrix4(object.matrixWorld);

            // Calculate bounding box of the specific clicked face in world coordinates
            const faceBoundingBox = new THREE.Box3();
            faceBoundingBox.setFromPoints([vA, vB, vC]);

            // Bounding box of the entire mesh object part that contains the face
            const objectPartBoundingBox = new THREE.Box3().setFromObject(object);
            const objectPartSize = new THREE.Vector3();
            objectPartBoundingBox.getSize(objectPartSize);

            // Prepare info text
            const selectedFaceInfoText = `
                <div class="p-2 bg-black text-white rounded text-xs mt-2 border border-gray-700">
                    <b>Selected Face: ${intersection.faceIndex}</b><br>
                    Clicked on: (${point.x.toFixed(3)}, ${point.y.toFixed(3)}, ${point.z.toFixed(3)})<br>
                    Area: ${objectPartSize.x.toFixed(3)} x ${objectPartSize.y.toFixed(3)} x ${objectPartSize.z.toFixed(3)}<br>
                    <div class="mt-1">
                        <b>Bounding Box (World Coords):</b><br>
                        X: [${faceBoundingBox.min.x.toFixed(3)}, ${faceBoundingBox.max.x.toFixed(3)}]<br>
                        Y: [${faceBoundingBox.min.y.toFixed(3)}, ${faceBoundingBox.max.y.toFixed(3)}]<br>
                        Z: [${faceBoundingBox.min.z.toFixed(3)}, ${faceBoundingBox.max.z.toFixed(3)}]
                    </div>
                </div>
            `;

            const infoElement = document.getElementById('model-info-text');
            if (infoElement) {
                // Ensure originalInfoContent is up-to-date
                if (!this.originalInfoContent || !this.originalInfoContent.includes(this.modelInfo.fileName) || this.modelInfo.fileName === '') {
                    this.updateInfoDisplay();
                }
                infoElement.innerHTML = this.originalInfoContent + selectedFaceInfoText;
            }

            // Setup flash effect for the highlight
            this.flashHighlightedFace();

        } else {
            // Clicked on empty space, restore original info display
            this.updateInfoDisplay();
        }
    }

    // Flash the highlighted face
    flashHighlightedFace() {
        if (!this.highlightFaceMesh) return;

        let flashCount = 0;
        const highlightMaterial = this.highlightFaceMesh.material;
        const originalOpacity = highlightMaterial.opacity;

        this.flashIntervalId = setInterval(() => {
            if (!this.highlightFaceMesh) {
                clearInterval(this.flashIntervalId);
                this.flashIntervalId = null;
                return;
            }

            // Stop after a few flashes
            if (flashCount >= 4) {
                clearInterval(this.flashIntervalId);
                this.flashIntervalId = null;
                highlightMaterial.opacity = originalOpacity;
                return;
            }

            // Toggle opacity for flash effect
            if (flashCount % 2 === 0) {
                highlightMaterial.opacity = 0.3; // Dim
            } else {
                highlightMaterial.opacity = originalOpacity; // Normal
            }

            flashCount++;
        }, 150);
    }

    // Setup grid
    setupGrid() {
        // Xóa grid cũ nếu có
        if (this.grid) {
            this.scene.remove(this.grid);
        }

        // Tạo grid mới
        const gridSize = 20;
        const gridDivisions = 20;
        const gridColor = 0x888888;
        this.grid = new THREE.GridHelper(gridSize, gridDivisions, gridColor, gridColor);
        this.grid.material.opacity = 0.25;
        this.grid.material.transparent = true;
        this.grid.position.y = -2; // Đặt grid ở dưới mô hình
        this.scene.add(this.grid);
    }
}

// Export for use in main.js
window.ModelViewer3D = ModelViewer3D;